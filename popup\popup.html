<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Automation</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <!-- Launch Interface - Shows initially -->
    <div id="launch-interface" class="launch-container">
        <div class="launch-content">
            <div class="launch-icon">🚀</div>
            <h2>LinkedIn Automation</h2>
            <p>Launch LinkedIn automation tools</p>
            <button id="launch-linkedin" class="btn-launch">Open LinkedIn & Start</button>
        </div>
    </div>

    <!-- Main Interface - Shows after LinkedIn opens -->
    <div id="main-interface" class="container hidden">
        <header>
            <h1>LinkedIn Auto Connect</h1>
            <div class="status" id="status">Ready</div>
        </header>

        <!-- Single Tab - Campaigns Only -->
        <nav class="tabs">
            <button class="tab-btn active" data-tab="campaigns">Campaigns</button>
        </nav>

        <!-- Campaigns Tab -->
        <div class="tab-content active" id="campaigns">
            <div class="section">
                <h3>Active Campaigns</h3>
                <div id="campaign-list">
                    <div class="empty-state">No campaigns yet. Create your first campaign!</div>
                </div>
                <button class="btn btn-primary" id="create-campaign">+ New Campaign</button>
            </div>


        </div>



        <!-- Campaign Creation Wizard -->
        <div class="modal" id="campaign-modal">
            <div class="modal-content campaign-wizard">
                <!-- Step 1: Campaign Name -->
                <div class="wizard-step active" id="step-1">
                    <div class="wizard-header">
                        <button class="back-btn hidden">&larr;</button>
                        <h3>Campaign name</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 1 out of 4</div>

                    <div class="form-group">
                        <input type="text" id="campaign-name" placeholder="Enter campaign name" class="campaign-input">
                    </div>

                    <div class="wizard-actions">
                        <button class="btn btn-primary wizard-next" id="next-step-1">NEXT</button>
                    </div>
                </div>

                <!-- Step 2: Add People Options -->
                <div class="wizard-step" id="step-2">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-1">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 2 out of 4</div>

                    <div class="add-people-options">
                        <button class="option-btn" id="linkedin-search-option">
                            <div class="option-title">ADD PEOPLE FROM LINKEDIN SEARCH</div>
                        </button>

                        <button class="option-btn" id="sales-navigator-option">
                            <div class="option-title">ADD PEOPLE FROM SALES NAVIGATOR</div>
                        </button>

                        <button class="option-btn" id="network-option">
                            <div class="option-title">ADD PEOPLE FROM MY NETWORK</div>
                        </button>

                        <div class="or-divider">Or</div>

                        <div class="csv-upload-area">
                            <div class="upload-text">Have a CSV file with your prospects' LinkedIn profile URLs?</div>
                            <button class="upload-btn" id="csv-upload-btn">Click to import the file</button>
                            <input type="file" id="csv-file-input" accept=".csv" class="hidden">
                            <div class="upload-hint">or drop it here</div>
                        </div>
                    </div>
                </div>

                <!-- Step 3: Network Search -->
                <div class="wizard-step" id="step-3-network">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-2-from-network">&larr;</button>
                        <h3>Add people from your network</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="network-search-options">
                        <div class="search-instructions">
                            <p>- Use native LinkedIn search filters to find people from your network. Scroll the filters to see more fields.</p>
                            <p>- Once specified, click "Apply LinkedIn filters" to see the results and then press "Start Collecting People" to collect the people.</p>
                        </div>

                        <div class="search-actions">
                            <button class="btn btn-secondary" id="show-network-filters">SHOW LINKEDIN NETWORK FILTERS</button>
                            <button class="btn btn-primary" id="start-network-collecting">START COLLECTING PEOPLE</button>
                        </div>

                        <div class="or-divider">Or</div>

                        <div class="network-list-option">
                            <h4>Browse Your Connections</h4>
                            <p>View and select from your existing LinkedIn connections</p>
                            <button class="btn btn-secondary" id="browse-connections">BROWSE MY CONNECTIONS</button>
                        </div>
                    </div>
                </div>

                <!-- Step 3: LinkedIn Search Filters -->
                <div class="wizard-step" id="step-3-search">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-step-2">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="search-instructions">
                        <p>- Use native LinkedIn search filters to specify your target audience. Scroll the filters to see more fields.</p>
                        <p>- Once specified, click "Apply LinkedIn filters" to see the results and then press "Start Collecting People" to collect the people.</p>
                    </div>

                    <div class="search-actions">
                        <button class="btn btn-secondary" id="show-filters">SHOW LINKEDIN FILTERS</button>
                        <button class="btn btn-primary" id="start-collecting">START COLLECTING PEOPLE</button>
                    </div>

                    <div class="or-divider">Or</div>

                    <div class="csv-upload-area">
                        <div class="upload-text">Have a CSV file with your prospects' LinkedIn profile URLs?</div>
                        <button class="upload-btn" id="csv-upload-btn-2">Click to import the file</button>
                        <div class="upload-hint">or drop it here</div>
                    </div>
                </div>

                <!-- Step 3: Collection Progress -->
                <div class="wizard-step" id="step-3-collecting">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-search">&larr;</button>
                        <h3>Add people</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 3 out of 4</div>

                    <div class="collection-status">
                        <div class="auto-detection-indicator hidden" id="auto-detection-indicator">
                            <span class="indicator-dot"></span>
                            <span>🔄 Auto-collecting profiles...</span>
                        </div>
                        <div class="collected-count">
                            <span>Collected: <span id="collected-number">0</span></span>
                            <button class="btn btn-secondary" id="pause-collection">PAUSE</button>
                        </div>

                        <div class="collected-profiles" id="collected-profiles-list">
                            <!-- Profiles will be added here dynamically -->
                        </div>

                        <button class="btn btn-primary hidden" id="next-to-messaging">NEXT</button>
                    </div>
                </div>

                <!-- Step 4: Profile Selection & Message Generation -->
                <div class="wizard-step" id="step-4-messaging">
                    <div class="wizard-header">
                        <button class="back-btn" id="back-to-collecting">&larr;</button>
                        <h3>Select Profiles & Generate Messages</h3>
                        <span class="close" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                    </div>
                    <div class="step-indicator">Create campaign: step 4 out of 4</div>

                    <div class="profile-selection-section">
                        <div class="selection-header">
                            <h4>Select profiles to generate messages for:</h4>
                            <div class="selection-controls">
                                <button class="btn btn-small" id="select-all-step4">Select All</button>
                                <button class="btn btn-small" id="deselect-all-step4">Deselect All</button>
                                <span class="selected-count">Selected: <span id="selected-count-step4">0</span></span>
                            </div>
                        </div>

                        <div class="profiles-selection-list" id="profiles-selection-list">
                            <!-- Profile checkboxes will be added here -->
                        </div>

                        <div class="auto-processing-settings" style="margin: 15px 0; padding: 10px; background: #f8f9fa; border-radius: 5px;">
                            <label style="display: flex; align-items: center; font-size: 14px;">
                                <input type="checkbox" id="auto-process-toggle" style="margin-right: 8px;">
                                Auto-start processing when 10 profiles are collected
                            </label>
                        </div>

                        <div class="generation-controls">
                            <button class="btn btn-success" id="auto-process-all-btn">
                                🚀 Auto-Process All (Select All + Generate + Send)
                            </button>
                        </div>

                        <!-- Auto-Processing Status -->
                        <div class="auto-process-status" id="auto-process-status" style="display: none; margin: 15px 0; padding: 10px; background: #e3f2fd; border-radius: 5px; border-left: 4px solid #2196f3;">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="status-text" id="auto-process-status-text">Processing profiles...</div>
                                <button class="btn btn-small btn-danger" id="stop-auto-process-btn" style="padding: 4px 8px; font-size: 12px;">Stop</button>
                            </div>
                            <div class="progress-bar" style="width: 100%; height: 6px; background: #e0e0e0; border-radius: 3px; margin-top: 8px;">
                                <div class="progress-fill" id="auto-process-progress" style="height: 100%; background: #2196f3; border-radius: 3px; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>

                        <!-- Message Generation Results -->
                        <div class="message-results hidden" id="message-results">
                            <h4>Generated Messages:</h4>
                            <div class="messages-container" id="messages-container">
                                <!-- Generated messages will appear here -->
                            </div>
                            <div class="message-actions">
                                <button class="btn btn-success" id="use-selected-messages">Use Selected Messages for Campaign</button>
                                <button class="btn btn-secondary" id="regenerate-messages">Regenerate Messages</button>
                            </div>
                        </div>

                        <div class="campaign-creation">
                            <button class="btn btn-primary" id="create-campaign-final">CREATE CAMPAIGN</button>
                        </div>
                    </div>
                </div>

                <!-- Duplicates Modal -->
                <div class="duplicates-overlay hidden" id="duplicates-modal">
                    <div class="duplicates-content">
                        <h3><span id="duplicate-count">0</span> duplicates found</h3>
                        <p>The following people already appeared in one of your campaigns or you contacted them before.</p>
                        <p>They will be excluded automatically.</p>

                        <div class="duplicate-profiles" id="duplicate-profiles-list">
                            <!-- Duplicate profiles will be shown here -->
                        </div>

                        <div class="duplicate-actions">
                            <button class="btn btn-secondary" id="cancel-duplicates">CANCEL</button>
                            <button class="btn btn-primary" id="exclude-duplicates">EXCLUDE</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Collection Modal -->
        <div class="modal" id="profiles-modal">
            <div class="modal-content">
                <span class="close" id="close-profiles" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
                <h3>Collected Profiles</h3>
                <div id="profiles-list"></div>
                <div class="modal-actions">
                    <button class="btn btn-secondary" id="export-profiles">Export CSV</button>
                    <button class="btn btn-primary" id="create-campaign-from-profiles">Create Campaign</button>
                </div>
            </div>
        </div>

        <footer>
            <div class="stats">
                <span>Today: <span id="today-count">0</span> connections</span>
                <span>Total: <span id="total-count">0</span> connections</span>
            </div>
        </footer>
    </div>

    <!-- Profile URLs Popup Modal -->
    <div id="profile-urls-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Collected Profile URLs</h3>
                <span class="close" id="close-profile-urls" title="Close button disabled. Use Ctrl+Shift+X to force close all modals.">&times;</span>
            </div>
            <div class="modal-body">
                <div class="profile-urls-container">
                    <div class="collection-status">
                        <p>Found <span id="profile-count-display">0</span> profiles</p>
                        <button class="btn btn-primary" id="add-profiles-to-campaign">Add Selected to Campaign</button>
                        <button class="btn btn-secondary" id="select-all-profiles">Select All</button>
                    </div>
                    <div class="profile-urls-list" id="profile-urls-list">
                        <!-- Profile URLs will be populated here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
